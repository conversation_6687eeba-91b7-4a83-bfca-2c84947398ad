import Image from 'next/image';

export default function ZajeciaOnlinePage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Zajęcia Jogi Online - <PERSON>",
    "description": "Prywatne lekcje jogi online i grupowe zajęcia online z certyfikowaną instruktorką jogi RYT 500.",
    "provider": {
      "@type": "Person",
      "name": "<PERSON>"
    }
  };

  // BAKASANA Online Classes Data - Ultra-Minimal
  const privateClasses = {
    title: "Indywidualne Sesje",
    subtitle: "Przestrzeń tylko dla Ciebie",
    description: "Spersonalizowane praktyki dostosowane do Twojego rytmu i potrzeb duszy",
    features: [
      "Wstępna rozmowa o intencjach",
      "Program stworzony specjalnie dla Ciebie",
      "Delikatne korekty w czasie rzeczywistym",
      "Nagranie do kontynuacji praktyki",
      "Wsparcie między sesjami"
    ],
    offerings: [
      { name: "Pier<PERSON>za sesja", duration: "30 min", note: "Poznanie i intencje" },
      { name: "Sesja indywidualna", duration: "60 min", note: "Pełna praktyka" },
      { name: "Cykl 4 sesji", duration: "60 min", note: "Pogłębienie praktyki" },
      { name: "Cykl 8 sesji", duration: "60 min", note: "Transformacja" }
    ]
  };

  const groupClasses = {
    title: "Wspólne Praktyki",
    subtitle: "Energia grupy, indywidualna uwaga",
    description: "Małe, intymne grupy gdzie każda osoba jest widziana i wspierana",
    schedule: [
      { day: "Poniedziałek", time: "18:00", type: "Hatha", level: "dla początkujących" },
      { day: "Środa", time: "19:00", type: "Vinyasa", level: "dla praktykujących" },
      { day: "Piątek", time: "17:30", type: "Yin", level: "dla wszystkich" },
      { day: "Sobota", time: "10:00", type: "Terapeutyczna", level: "dla ciała i duszy" }
    ],
    offerings: [
      { name: "Małe grupy", size: "4-6 osób", note: "Intymna atmosfera" },
      { name: "Średnie grupy", size: "7-12 osób", note: "Energia wspólnoty" },
      { name: "Miesięczny cykl", size: "8 praktyk", note: "Regularna praktyka" },
      { name: "Kwartalny cykl", size: "24 praktyki", note: "Głęboka transformacja" }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <main className="bg-sanctuary min-h-screen">
        {/* HERO SECTION - Ultra-Minimal */}
        <section className="hero">
          <div className="hero-content">
            <h1 className="hero-title">
              ONLINE
            </h1>

            <p className="hero-subtitle">
              Praktykuj z domu
            </p>

            <div className="hero-meta">
              Gdzie chcesz • Kiedy chcesz
            </div>
          </div>
        </section>

        {/* PRIVATE CLASSES SECTION - Ultra-Minimal */}
        <section className="container">
          <div className="about-container" style={{alignItems: 'flex-start'}}>
            {/* Content */}
            <div>
              <h2 className="section-header" style={{marginBottom: '32px'}}>
                {privateClasses.title}
              </h2>

              <p className="about-intro" style={{marginBottom: '32px'}}>
                "{privateClasses.subtitle}"
              </p>

              <div className="body-text" style={{marginBottom: '48px'}}>
                {privateClasses.description}
              </div>

              {/* Features - Minimal List */}
              <div style={{marginBottom: '48px'}}>
                <h3 className="card-title" style={{
                  fontSize: '18px',
                  marginBottom: '24px',
                  opacity: '0.8'
                }}>
                  Co obejmuje praktyka:
                </h3>
                <div style={{display: 'flex', flexDirection: 'column', gap: '16px'}}>
                  {privateClasses.features.map((feature, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '16px'
                    }}>
                      <div style={{
                        width: '1px',
                        height: '1px',
                        background: 'var(--temple-gold)',
                        marginTop: '12px',
                        flexShrink: '0',
                        opacity: '0.4'
                      }}></div>
                      <span className="body-text" style={{fontSize: '14px'}}>
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Offerings - Hidden Pricing */}
              <div>
                <h3 className="card-title" style={{
                  fontSize: '18px',
                  marginBottom: '24px',
                  opacity: '0.8'
                }}>
                  Formy praktyki:
                </h3>
                <div style={{display: 'flex', flexDirection: 'column', gap: '16px'}}>
                  {privateClasses.offerings.map((option, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      paddingBottom: '16px',
                      borderBottom: '1px solid var(--stone)',
                      borderBottomOpacity: '0.1'
                    }}>
                      <div>
                        <h4 className="body-text" style={{
                          fontWeight: '400',
                          marginBottom: '4px'
                        }}>
                          {option.name}
                        </h4>
                        <p className="subtle-text" style={{fontSize: '10px'}}>
                          {option.duration}
                        </p>
                      </div>
                      <span className="subtle-text" style={{
                        fontSize: '10px',
                        opacity: '0.6'
                      }}>
                        {option.note}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Image */}
            <div className="enterprise-image" style={{position: 'relative'}}>
              <Image
                src="/images/profile/omnie-opt.webp"
                alt="Indywidualne sesje jogi online"
                fill
                className="about-image object-cover"
                sizes="(max-width: 1024px) 100vw, 50vw"
                quality={95}
              />
            </div>
          </div>
        </section>

        <div className="section-divider" style={{margin: '120px auto'}}></div>

        {/* GROUP CLASSES SECTION - Ultra-Minimal */}
        <section className="container">
          <div className="about-container" style={{
            alignItems: 'flex-start',
            flexDirection: 'row-reverse'
          }}>
            {/* Content */}
            <div>
              <h2 className="section-header" style={{marginBottom: '32px'}}>
                {groupClasses.title}
              </h2>

              <p className="about-intro" style={{marginBottom: '32px'}}>
                "{groupClasses.subtitle}"
              </p>

              <div className="body-text" style={{marginBottom: '48px'}}>
                {groupClasses.description}
              </div>

              {/* Schedule - Minimal */}
              <div style={{marginBottom: '48px'}}>
                <h3 className="card-title" style={{
                  fontSize: '18px',
                  marginBottom: '24px',
                  opacity: '0.8'
                }}>
                  Harmonogram praktyk:
                </h3>
                <div style={{display: 'flex', flexDirection: 'column', gap: '16px'}}>
                  {groupClasses.schedule.map((session, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      paddingBottom: '16px',
                      borderBottom: '1px solid var(--stone)',
                      borderBottomOpacity: '0.1'
                    }}>
                      <div>
                        <h4 className="body-text" style={{
                          fontWeight: '400',
                          marginBottom: '4px'
                        }}>
                          {session.day} {session.time}
                        </h4>
                        <p className="subtle-text" style={{fontSize: '10px'}}>
                          {session.type}
                        </p>
                      </div>
                      <span className="subtle-text" style={{
                        fontSize: '10px',
                        opacity: '0.6'
                      }}>
                        {session.level}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Offerings - Hidden Pricing */}
              <div>
                <h3 className="card-title" style={{
                  fontSize: '18px',
                  marginBottom: '24px',
                  opacity: '0.8'
                }}>
                  Formy uczestnictwa:
                </h3>
                <div style={{display: 'flex', flexDirection: 'column', gap: '16px'}}>
                  {groupClasses.offerings.map((option, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      paddingBottom: '16px',
                      borderBottom: '1px solid var(--stone)',
                      borderBottomOpacity: '0.1'
                    }}>
                      <div>
                        <h4 className="body-text" style={{
                          fontWeight: '400',
                          marginBottom: '4px'
                        }}>
                          {option.name}
                        </h4>
                        <p className="subtle-text" style={{fontSize: '10px'}}>
                          {option.size}
                        </p>
                      </div>
                      <span className="subtle-text" style={{
                        fontSize: '10px',
                        opacity: '0.6'
                      }}>
                        {option.note}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Minimal Visual Element */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '400px',
              background: 'var(--rice)',
              position: 'relative'
            }}>
              <div style={{textAlign: 'center'}}>
                <div style={{
                  fontSize: '32px',
                  color: 'var(--temple-gold)',
                  opacity: '0.4',
                  marginBottom: '16px'
                }}>
                  ॐ
                </div>
                <span className="subtle-text" style={{
                  fontSize: '14px',
                  fontWeight: '300'
                }}>
                  Wspólne praktyki
                </span>
              </div>
            </div>
          </div>
        </section>

        <div className="section-divider" style={{margin: '120px auto'}}></div>

        {/* CALL TO ACTION - Ultra-Minimal */}
        <section className="container" style={{textAlign: 'center', paddingBottom: '120px'}}>
          <h3 className="section-header" style={{marginBottom: '32px'}}>
            Gotowa na pierwszą praktykę?
          </h3>

          <div className="body-text" style={{
            maxWidth: '600px',
            margin: '0 auto 48px auto',
            opacity: '0.8'
          }}>
            Skontaktuj się ze mną, aby rozpocząć swoją podróż z jogą online
          </div>

          {/* CONTACT BUTTONS - Ghost Style */}
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '24px',
            alignItems: 'center',
            maxWidth: '400px',
            margin: '0 auto'
          }}>
            <a
              href="https://app.fitssey.com/Flywithbakasana/frontoffice"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost btn-primary"
              aria-label="Umów sesję przez Fitssey"
            >
              Umów Sesję przez Fitssey
            </a>

            <span className="subtle-text" style={{fontSize: '10px'}}>lub</span>

            <a
              href="/kontakt"
              className="btn-ghost btn-accent"
              aria-label="Skontaktuj się bezpośrednio"
            >
              Kontakt Bezpośredni
            </a>
          </div>
        </section>
      </main>
    </>
  );
}
