@tailwind base;
@tailwind components;
@tailwind utilities;

/* =============================================
   🏛️ BAKASANA - ENTERPRISE 11/10 DESIGN SYSTEM
   Ultra-minimalist perfection meets spiritual elegance
   ============================================= */

/* ULTRA-MINIMAL FONT LOADING - WHISPER WEIGHTS */
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,200;0,300;0,400;0,500;1,200;1,300;1,400&family=Inter:wght@100;200;300;400;500&display=swap');

:root {
  /* ===== MASTER GRID & SPACING SYSTEM - ULTRA BREATHING ===== */
  --container-max: 1200px;
  --section-padding: 180px 0;      /* Increased from 120px for more breathing space */
  --element-breathing: 10%;         /* Increased from 8% for ultra-luxury spacing */
  --card-internal: 60px;            /* Increased from 48px */
  --micro-spacing: 32px;            /* Increased from 24px */
  --nano-spacing: 12px;             /* Increased from 8px */
  --hero-spacing: 120px;            /* New variable for hero section spacing */
  --whisper-spacing: 180px;         /* New variable for ultra-minimal sections */

  /* ===== RESPONSIVE BREAKPOINTS ===== */
  --mobile: 375px;
  --tablet: 768px;
  --desktop: 1024px;
  --large: 1440px;
  --ultra: 1920px;

  /* ===== PRECISE COLOR PALETTE - WHISPER AESTHETIC ===== */
  /* Główne kolory */
  --sanctuary: #FDFCF8;         /* Tło główne - mleczny */
  --charcoal: #3A3A3A;          /* Tekst główny - ciepły antracyt */
  --stone: #A8A8A8;             /* Tekst drugorządny - lighter for whisper effect */
  --stone-light: #C8C8C8;       /* Even lighter stone for ultra-subtle elements */
  --whisper: #F9F7F2;           /* Footer - bardzo jasny beż */
  --rice: #F5F3EF;              /* Alternating sections */

  /* Duchowe akcenty */
  --temple-gold: #C4A575;       /* Delikatny złoty */
  --sage-green: #7C9885;        /* Tarasy ryżowe */
  --ocean-blue: #4A6B7C;        /* Sri Lanka */
  --om-symbol: #D4A574;         /* Elementy duchowe */
  --lotus-pink: #E8D5D0;        /* Subtelne akcenty */
  --incense: #A8A5A0;           /* Neutralne elementy */

  /* Transparencje */
  --glass-nav: rgba(253, 252, 248, 0.95);
  --subtle-shadow: rgba(0, 0, 0, 0.02);
  --hover-overlay: rgba(0, 0, 0, 0.1);

  /* ===== TYPOGRAPHY SCALE - ULTRA-MINIMAL HIERARCHY ===== */
  /* Font families */
  --font-primary: 'Cormorant Garamond', serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Typography scale - Expanded for whisper aesthetic */
  --text-xs: 10px;              /* Ultra-small for whisper elements */
  --text-sm: 12px;              /* Small details */
  --text-base: 14px;            /* Body text - smaller for elegance */
  --text-lg: 16px;              /* Slightly larger body */
  --text-xl: 20px;              /* Subtitles */
  --text-2xl: 28px;             /* Section headers */
  --text-3xl: 36px;             /* Page headers */
  --text-4xl: 42px;             /* Large headers */
  --text-5xl: 56px;             /* Hero subtitles */
  --text-6xl: 72px;             /* Standard hero */
  --text-7xl: 96px;             /* Large hero */
  --text-8xl: 140px;            /* Ultra hero - BAKASANA title */

  /* Ultra-light font weights for whisper aesthetic */
  --font-ultra-light: 100;
  --font-whisper: 200;          /* Primary weight for hero */
  --font-light: 300;
  --font-normal: 400;

  /* Opacity levels for whisper effect */
  --opacity-whisper: 0.4;       /* Ultra-subtle elements */
  --opacity-subtle: 0.6;        /* Subtle elements */
  --opacity-soft: 0.7;          /* Soft hover states */
  --opacity-visible: 0.85;      /* Main hero title */

  /* Aliasy dla kompatybilności */
  --primary: var(--charcoal);
  --secondary: var(--stone);
  --background: var(--sanctuary);
  --accent: var(--temple-gold);
}

/* ===== GLOBAL RULES FOR ULTRA-MINIMALISM ===== */
* {
  box-sizing: border-box;
  border-radius: 0 !important; /* Zero rounded corners globally - BAKASANA RULE */
}

/* ENFORCE ZERO BORDER-RADIUS ON ALL ELEMENTS */
*,
*::before,
*::after,
button,
input,
textarea,
select,
.btn,
.button,
.card,
.badge,
.rectangular,
.rectangular-subtle,
.elegant-border {
  border-radius: 0 !important;
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-variant-ligatures: common-ligatures;
  font-kerning: normal;
}

body {
  font-family: 'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--sanctuary);
  overflow-x: hidden;
}

/* ===== PRECISE TYPOGRAPHY HIERARCHY ===== */

/* LOGO */
.logo {
  font-family: 'Cormorant Garamond', serif;
  font-size: 22px;
  font-weight: 400;
  letter-spacing: 0.5px;
  color: var(--charcoal);
}

/* HERO TITLE - ULTRA-MINIMAL WHISPER */
.hero-title {
  font-family: var(--font-primary);
  font-size: clamp(72px, 10vw, var(--text-8xl)); /* 140px on desktop */
  font-weight: var(--font-whisper);               /* Ultra-light 200 */
  letter-spacing: 0.25em;                         /* More breathing space */
  line-height: 1.0;                               /* Tighter for impact */
  color: var(--charcoal);
  opacity: var(--opacity-visible);                /* 0.85 - barely visible effect */
  position: relative;
  top: -20px;                                     /* Slightly higher positioning */
}

/* SECTION HEADERS */
.section-header {
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(36px, 5vw, 56px);
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.3;
  color: var(--charcoal);
}

/* CARD TITLES */
.card-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 28px;
  font-weight: 400;
  letter-spacing: 0.05em;
  line-height: 1.4;
  color: var(--charcoal);
}

/* BODY TEXT */
.body-text {
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  letter-spacing: 0.3px;
  line-height: 1.8;
  color: var(--charcoal);
}

/* NAVIGATION LINKS */
.nav-link {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 300;
  letter-spacing: 0.3px;
  text-transform: none;
  color: var(--charcoal);
}

/* SUBTLE TEXT */
.subtle-text {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  color: var(--stone);
}

/* Standard HTML elements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 300;
  letter-spacing: 0.05em;
  line-height: 1.3;
  color: var(--charcoal);
  margin-bottom: 1.5rem;
}

h1 { font-size: clamp(64px, 8vw, 128px); letter-spacing: 0.15em; line-height: 1.1; }
h2 { font-size: clamp(36px, 5vw, 56px); letter-spacing: 0.08em; }
h3 { font-size: 28px; letter-spacing: 0.05em; }

p {
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  letter-spacing: 0.3px;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: var(--charcoal);
}

/* ===== NAVIGATION - PIXEL PERFECT ===== */
.navigation {
  position: fixed;
  top: 0;
  width: 100%;
  padding: 32px 8%;
  z-index: 100;
  background: transparent;
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}

.navigation.scrolled {
  background: var(--glass-nav);
  backdrop-filter: blur(20px);
  padding: 20px 8%;
}

.nav-container {
  max-width: var(--container-max);
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-links {
  display: flex;
  gap: 48px;
  list-style: none;
}

.nav-link {
  transition: opacity 300ms ease;
}

.nav-link:hover {
  opacity: 0.6;
}

/* Links - Ultra-minimal hover effects */
a {
  color: inherit;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

a:hover {
  opacity: 0.7; /* Only opacity changes on hover */
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  width: 100%;
}

/* ===== HERO SECTION - ULTRA-MINIMAL PERFECTION ===== */
.hero {
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: linear-gradient(135deg, var(--sanctuary) 0%, var(--whisper) 50%, var(--rice) 100%);
  position: relative;
  padding: 0 var(--element-breathing);
}

.hero-content {
  max-width: 900px;
  padding: 0;
  position: relative;
}

/* Hero subtitle - whisper effect */
.hero-subtitle {
  font-family: var(--font-secondary);
  font-size: var(--text-sm);                     /* 12px - smaller */
  font-weight: var(--font-whisper);              /* 200 weight */
  color: var(--stone);
  opacity: var(--opacity-subtle);                /* 0.6 opacity */
  margin: var(--hero-spacing) 0 0 0;             /* 120px top spacing */
  letter-spacing: 0.2em;
  text-transform: none;
}

/* Hero meta - locations at bottom */
.hero-meta {
  font-family: var(--font-secondary);
  font-size: 11px;                               /* Even smaller */
  font-weight: var(--font-whisper);              /* 200 weight */
  color: var(--stone);
  opacity: var(--opacity-whisper);               /* 0.4 - barely visible */
  letter-spacing: 0.3em;
  text-transform: none;
  position: absolute;
  bottom: 15vh;                                   /* 85% height positioning */
  left: 50%;
  transform: translateX(-50%);
  animation: fadeInDelayed 2s ease-out forwards;
  opacity: 0;                                     /* Start invisible */
}

/* Fade-in animation for locations */
@keyframes fadeInDelayed {
  0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
  100% { opacity: var(--opacity-whisper); transform: translateX(-50%) translateY(0); }
}

.subtle-link {
  font-size: 12px;
  font-weight: 300;
  color: var(--stone);
  text-decoration: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border-bottom: 1px solid transparent;
  transition: all 300ms ease;
}

.subtle-link:hover {
  opacity: 0.7;
  border-bottom-color: var(--stone);
}

/* ===== LAYOUT SYSTEM - Przestrzeń jako Luksus ===== */
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 120px 8%; /* 8% marginesy boczne - każdy element oddycha */
}

.section {
  padding: var(--section-padding); /* 120px między sekcjami minimum */
  background: transparent; /* Zero-container philosophy */
}

.section-breathe {
  padding: 180px 15%; /* Hojne odstępy dla luksusowego odczucia */
}

/* ===== DESTINATION CARDS - POETYCKIE KARTY ===== */
.destinations {
  padding: var(--section-padding);
  background: var(--sanctuary);
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 80px;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 8%;
}

.destination-card {
  background: transparent;
  border: none;
  border-radius: 0;
  box-shadow: 0 2px 8px var(--subtle-shadow);
  transition: all 400ms ease;
  overflow: hidden;
}

.destination-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

.card-image {
  aspect-ratio: 4/3;
  background-size: cover;
  background-position: center;
  filter: grayscale(1) contrast(0.9) brightness(1.1);  /* Full grayscale initially */
  transition: all 600ms ease;
  position: relative;
}

.destination-card:hover .card-image {
  filter: grayscale(0.2) contrast(1) brightness(1);    /* Slight color on hover */
}

/* Hidden overlay with details */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 400ms ease;
  padding: 32px;
  text-align: center;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.1em;
}

.destination-card:hover .card-overlay {
  opacity: 1;
}

.card-content {
  padding: 48px 32px;
  text-align: center;
}

.card-meta {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 16px;
  opacity: 0;                                           /* Hidden initially */
  transition: opacity 300ms ease;
}

.destination-card:hover .card-meta {
  opacity: 1;                                           /* Show on hover */
}

.card-description {
  margin: 24px 0 32px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0;                                           /* Hidden initially */
  transition: opacity 300ms ease;
}

.destination-card:hover .card-description {
  opacity: 0.8;                                         /* Show on hover */
}

.card-details {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  text-decoration: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  opacity: var(--opacity-whisper);                      /* Ultra-subtle */
  transition: opacity 300ms ease;
  position: absolute;
  bottom: 16px;
  right: 16px;
}

.card-details:hover {
  opacity: var(--opacity-soft);
}

/* ===== GHOST BUTTONS - DUCHOWA PROSTOTA ===== */
.btn-ghost {
  padding: 16px 48px;
  border: 1px solid var(--stone);
  background: transparent;
  color: var(--stone);
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 300;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 0;
  transition: opacity 300ms ease;
  cursor: pointer;
}

.btn-ghost:hover {
  opacity: 0.7;
}

.btn-primary {
  border-color: var(--charcoal);
  color: var(--charcoal);
}

.btn-accent {
  border-color: var(--temple-gold);
  color: var(--temple-gold);
}

/* ===== ABOUT JULIA SECTION - AUTENTYCZNOŚĆ ===== */
.about-julia {
  padding: var(--section-padding);
  background: var(--rice);
}

.about-container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 8%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.about-image {
  filter: grayscale(1) contrast(1.1);
  transition: all 500ms ease;
  border-radius: 0;
}

.about-image:hover {
  filter: grayscale(0) contrast(1);
}

.about-content {
  padding: 0 40px;
}

.about-intro {
  font-size: 18px;
  font-weight: 300;
  color: var(--stone);
  margin-bottom: 32px;
  font-style: italic;
  line-height: 1.6;
}

.spiritual-stats {
  display: flex;
  gap: 60px;                    /* Increased from 40px for more breathing space */
  margin-top: 48px;
  justify-content: center;
}

.stat-item {
  text-align: center;
}

.stat-symbol {
  font-size: 28px;              /* Slightly larger symbols */
  color: var(--temple-gold);
  margin-bottom: 12px;          /* More space below symbols */
  opacity: 0.8;                 /* Subtle opacity for elegance */
}

.stat-label {
  font-size: 12px;
  font-weight: 300;
  color: var(--stone);
  letter-spacing: 0.1em;
  text-transform: lowercase;    /* Lowercase for more intimate feel */
}

.stat-label {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* ===== FOOTER - MINIMALIZM ===== */
.footer {
  background: var(--whisper);
  padding: 80px 8% 40px 8%;
  text-align: center;
}

.footer-content {
  max-width: 600px;
  margin: 0 auto;
}

.spiritual-greeting {
  font-family: 'Cormorant Garamond', serif;
  font-size: 18px;
  font-weight: 300;
  color: var(--temple-gold);
  margin-bottom: 48px;
  letter-spacing: 1px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-bottom: 32px;
}

.footer-link {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  text-decoration: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: opacity 300ms ease;
}

.footer-link:hover {
  opacity: 0.6;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 32px;
}

.social-icon {
  width: 20px;
  height: 20px;
  opacity: 0.4;
  transition: opacity 300ms ease;
}

.social-icon:hover {
  opacity: 0.8;
}

.copyright {
  font-size: 10px;
  font-weight: 300;
  color: var(--stone);
  opacity: 0.5;
  letter-spacing: 0.3px;
}

/* SECTION DIVIDERS - MINIMAL */
.section-divider {
  width: 60px;
  height: 1px;
  background: var(--stone);
  margin: 0 auto;
  opacity: 0.3;
}

/* ===== BAKASANA UTILITY CLASSES ===== */

/* Typography utilities */
.font-cormorant { font-family: var(--font-primary); }
.font-inter { font-family: var(--font-secondary); }
.letter-spacing-wide { letter-spacing: 0.1em; }
.letter-spacing-wider { letter-spacing: 0.2em; }

/* Color utilities */
.text-sanctuary { color: var(--sanctuary); }
.text-charcoal { color: var(--charcoal); }
.text-stone { color: var(--stone); }
.text-temple-gold { color: var(--temple-gold); }
.text-sage-green { color: var(--sage-green); }
.bg-sanctuary { background-color: var(--sanctuary); }
.bg-charcoal { background-color: var(--charcoal); }
.bg-whisper { background-color: var(--whisper); }
.bg-rice { background-color: var(--rice); }

/* Spacing utilities */
.section-padding { padding: var(--section-padding); }
.container-padding { padding: 0 var(--element-breathing); }
.breathe-spacing { padding: var(--breathe-spacing) 0; }

/* Layout utilities */
.text-center { text-align: center; }
.max-width-content { max-width: var(--container-max); margin: 0 auto; }

/* Opacity utilities for whisper effects */
.opacity-whisper { opacity: var(--opacity-whisper); }
.opacity-subtle { opacity: var(--opacity-subtle); }
.opacity-soft { opacity: var(--opacity-soft); }
.opacity-visible { opacity: var(--opacity-visible); }

/* ===== RESPONSIVE SYSTEM ===== */

/* Mobile (375px-767px) */
@media (max-width: 767px) {
  .nav-links { gap: 24px; }
  .hero-title { font-size: 48px; }
  .destinations-grid {
    grid-template-columns: 1fr;
    gap: 48px;
  }
  .about-container {
    grid-template-columns: 1fr;
    gap: 48px;
  }
  .container { padding: 80px 5%; }
  .section { padding: 80px 0; }
}

/* Tablet (768px-1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .nav-links { gap: 32px; }
  .hero-title { font-size: 72px; }
  .destinations-grid { gap: 60px; }
}

/* ===== KLUCZOWE ZASADY IMPLEMENTACJI ===== */

/* 1. Zero Rounded Corners */
* { border-radius: 0 !important; }

/* 2. Tylko Opacity Hovers */
.hover-element:hover {
  opacity: 0.7;
  /* NIGDY: transform, color, background, shadow */
}

/* 3. Performance Optimizations */
.smooth-transition {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.gpu-acceleration {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 4. Enterprise Accessibility */
.focus-visible,
*:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  border-radius: 0;
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  font-size: 14px;
  font-weight: 300;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-ghost {
    border-width: 2px;
  }

  .nav-link {
    text-decoration: underline;
  }

  .card-image {
    filter: contrast(1.2);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .mandala-outer,
  .mandala-middle,
  .mandala-inner {
    animation: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --sanctuary: #1a1a1a;
    --charcoal: #e0e0e0;
    --stone: #a0a0a0;
    --whisper: #2a2a2a;
  }
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--stone);
  opacity: 0.3;
}

/* ===== SPIRITUAL ELEMENTS ===== */

/* Om Symbol Styling */
.om-symbol {
  font-family: 'Noto Sans Devanagari', serif;
  color: var(--om-symbol);
  font-size: 1.5rem;
  opacity: 0.8;
  display: inline-block;
}

/* Balinese Greeting */
.balinese-greeting {
  font-style: italic;
  color: var(--temple-gold);
  font-size: 0.9rem;
  letter-spacing: 0.05em;
  font-family: 'Inter', sans-serif;
  font-weight: 300;
}

/* Cultural Text Accents */
.temple-gold-text {
  color: var(--temple-gold);
  font-weight: 400;
}

.sage-text {
  color: var(--sage-green);
  font-weight: 400;
}

.sri-lankan-accent {
  color: var(--ocean-blue);
  font-weight: 400;
}

/* ===== LOTUS DECORATIVE ELEMENTS ===== */
.lotus-divider {
  text-align: center;
  margin: 60px 0;
  color: var(--sage-green);
  font-size: 1.2rem;
  opacity: 0.6;
}

.lotus-divider::before,
.lotus-divider::after {
  content: '❀';
  margin: 0 20px;
  opacity: 0.4;
}

/* ===== RETREAT CARD STYLING - ULTRA-MINIMAL ===== */
.retreat-card {
  background: transparent;
  border: none;
  margin-bottom: 80px;
  transition: opacity 0.2s ease;
}

.retreat-card:hover {
  opacity: 0.9;
}

.retreat-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 0; /* Sharp edges */
  display: block;
}

.retreat-image-container {
  margin-bottom: 30px;
  overflow: hidden;
}

/* ===== ENTERPRISE ENHANCEMENTS ===== */

/* Sacred Quote Styling */
.sacred-quote {
  font-family: 'Cormorant Garamond', serif;
  font-style: italic;
  font-weight: 300;
  letter-spacing: 0.02em;
  line-height: 1.6;
  color: var(--stone);
}

/* Enterprise Image Treatments */
.enterprise-image {
  position: relative;
  overflow: hidden;
}

.enterprise-image img {
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.enterprise-image:hover img {
  transform: scale(1.05);
}

/* Sacred Dividers */
.sacred-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 4rem 0;
}

.sacred-divider::before,
.sacred-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--stone);
  opacity: 0.3;
}

.sacred-divider-content {
  margin: 0 2rem;
  color: var(--stone);
  opacity: 0.6;
  font-size: 1.5rem;
}

/* ===== BLOG SECTION - ASYMMETRIC POETRY ===== */
.blog-asymmetric-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 100px;                           /* Large gaps for breathing space */
  max-width: var(--container-max);
  margin: 0 auto;
}

/* Different heights for organic asymmetry */
.blog-card-1 {
  transform: translateY(-20px);         /* First card higher */
}

.blog-card-2 {
  transform: translateY(40px);          /* Second card lower */
}

.blog-card-3 {
  transform: translateY(10px);          /* Third card slightly higher */
}

/* Blog card specific styling */
.blog-card .card-image {
  filter: sepia(0.2) contrast(0.9);    /* Stonowane images */
  transition: filter 500ms ease;
}

.blog-card:hover .card-image {
  filter: sepia(0.1) contrast(1);
}

/* Hide dates and make titles more poetic */
.blog-card .card-meta {
  display: none;                        /* Hide dates completely */
}

.blog-card .card-title {
  font-size: 24px;                     /* Slightly smaller for intimacy */
  font-weight: 300;
  line-height: 1.3;
}

.blog-card .card-description {
  font-size: 13px;                     /* Smaller excerpt text */
  line-height: 1.6;
  max-width: none;                      /* Remove width restrictions */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-asymmetric-grid {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .blog-card-1,
  .blog-card-2,
  .blog-card-3 {
    transform: none;                    /* Remove transforms on mobile */
  }
}